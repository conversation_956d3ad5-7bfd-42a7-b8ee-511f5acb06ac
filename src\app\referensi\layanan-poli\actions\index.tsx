import fetcher from "@/libs/fetch-api";
import { ResponsePoli } from "../../poli/_schema/typePoli";

export async function getPoliById() {
  return fetcher<ResponsePoli>({
    path: `/reference/polyclinic`,
    options: {
      next: {
        tags: ["poli-estetika-2"],
      },
    },
  });
}

export async function getPoliFilterById({ id }: { id: string }) {
  return fetcher<any>({
    path: `/reference/healthcare/service/filter?poly_id=${id}`,
    options: {
      next: {
        tags: ["poli-estetika-2"],
      },
    },
  });
}

export async function postLayananPoli({
  data,
  poly_id,
}: {
  data: any;
  poly_id: string;
}) {
  return fetcher<any>({
    path: `/reference/healthcare/service`,
    options: {
      method: "POST",
      body: JSON.stringify({ ...data, poly_id }),
    },
  });
}

export async function patchSwich({
  active,
  id,
}: {
  active: boolean;
  id: string;
}) {
  return fetcher<any>({
    path: `/reference/healthcare/service/${id}`,
    options: {
      method: "PATCH",
      body: JSON.stringify({ active }),
    },
  });
}

export async function patchLayananPoli({
  data,
  id,
  active,
}: {
  data: any;
  id: string;
  active: boolean;
}) {
  return fetcher<any>({
    path: `/reference/healthcare/service/${id}`,
    options: {
      method: "PATCH",
      body: JSON.stringify({ ...data, active }),
    },
  });
}

export async function getLayananPoliById({ id }: { id: string }) {
  return fetcher<any>({
    path: `/reference/healthcare/service/${id}`,
    options: {
      next: {
        tags: ["poli-estetika-2"],
      },
    },
  });
}
