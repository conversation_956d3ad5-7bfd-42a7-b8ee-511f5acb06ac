import { Suspense } from "react";
import { getLab } from "../laboratorium/actions";
import FilterPoli from "../poli/_components/filter";
import TablePemeriksaanLab from "./_components/table";
import LoadingTable from "@/components/loadingTable";

export default async function PagePemeriksaanLab() {
  const res = getLab();
  return (
    <div className="flex flex-col gap-4">
      <FilterPoli title="Nama Laboratorium" />
      <Suspense fallback={<LoadingTable />}>
        <TablePemeriksaanLab res={res} />
      </Suspense>
    </div>
  );
}
