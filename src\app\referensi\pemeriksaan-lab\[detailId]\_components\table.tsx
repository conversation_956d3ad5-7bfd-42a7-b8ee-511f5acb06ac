"use client";

import ButtonNavigation from "@/components/custom/buttonNavigation";
import TablePoliklinik from "@/components/table/tablePoliklinik";
import { SmoothSwitch } from "@/components/ui/switch-smooth";
import { PropsTable } from "@/interfaces/tables";
import { addToast, Chip, Switch } from "@heroui/react";
import { use, useCallback, useState } from "react";

export const users = [
  {
    id: 1,
    name: "<PERSON>",
    mahasiswa: "85000",
    pegawai: "20000",
    "kel-pegawai": "10000",
    umum: "20000",
    list: ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>awa<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> Mahasiswa"],
    pensiunan: "20000",
    status: "active",
  },
  {
    id: 2,
    name: "<PERSON>",
    mahasiswa: "85000",
    pegawai: "20000",
    "kel-pegawai": "10000",
    umum: "20000",
    list: ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>awa<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>iswa"],
    pensiunan: "20000",
    status: "active",
  },
  {
    id: 3,
    name: "<PERSON>",
    mahas<PERSON>wa: "85000",
    pegawai: "20000",
    "kel-pegawai": "10000",
    umum: "20000",
    list: ["Mahasisawa", "Pegawai", "Alumni", "Umum", "Keluarga Mahasiswa"],
    pensiunan: "20000",
    status: "active",
  },
];

const columns = [
  {
    key: "no",
    label: "NO",
  },
  {
    key: "name",
    label: "Nama Poli",
  },
  {
    key: "mahasiswa",
    label: "Mahasiswa",
  },
  {
    key: "pegawai",
    label: "Pegawai",
  },
  {
    key: "kel-pegawai",
    label: "Kel.Pegawai",
  },
  {
    key: "umum",
    label: "Umum",
  },
  {
    key: "pensiunan",
    label: "Pensiunan",
  },
  {
    key: "status",
    label: "Status",
  },
  {
    key: "action",
    label: "Aksi",
  },
];

interface Item {
  id: string;
  value: boolean;
}

export default function TableDetailPemeriksaanLab({
  detailId,
  res,
}: {
  detailId: string;
  res: any;
}) {
  const data = use(res);
  const result = data?.content;
  const pageInfo = data?.page;

  const [items, setItems] = useState<Item[]>([]);

  const upsertItem = useCallback((id: string, value: boolean) => {
    setItems((prevItems) => {
      const existingItemIndex = prevItems.findIndex((item) => item.id === id);

      if (existingItemIndex >= 0) {
        const newItems = [...prevItems];
        newItems[existingItemIndex] = { id, value };
        return newItems;
      }
      return [...prevItems, { id, value }];
    });
  }, []);

  const getItemById = useCallback(
    (id: string): boolean | null => {
      const item = items.find((item) => item.id === id);
      return item ? item.value : null;
    },
    [items]
  );

  const handleChangeSwitch = async ({
    id,
    currentSwitch,
  }: {
    id: string;
    currentSwitch: boolean;
  }) => {
    // try {
    //   const res = await patchtPoli({ active: currentSwitch, id: id });
    //   return res;
    // } catch (error: any) {
    //   addToast({
    //     title: "Error",
    //     color: "danger",
    //     description: error.message ?? "Terjadi kesalahan",
    //   });
    // }
  };

  const renderCell = useCallback(
    (items: any, column_key: any, index: number) => {
      const currentActive = getItemById(items.id) ?? items.active;
      switch (column_key) {
        case "no":
          return index + 1;
        case "name":
          return (
            <div className="flex flex-col gap-1">
              <p>{items.name}</p>
              <div className="flex items-center gap-2">
                {items.list.map((item: any, index: number) => (
                  <Chip key={index} size="sm">
                    {item}
                  </Chip>
                ))}
              </div>
            </div>
          );
        case "status":
          return (
            <div className="flex gap-2  items-center ">
              <SmoothSwitch
                id={items.id}
                isSelected={currentActive}
                onValueChange={(isSelected) => {
                  upsertItem(items.id, isSelected);
                  handleChangeSwitch({
                    id: items.id,
                    currentSwitch: isSelected,
                  });
                }}
              />
              <p className="capitalize">
                {currentActive ? "aktif" : "tidak aktif"}
              </p>
            </div>
          );
        case "action":
          return (
            <ButtonNavigation
              href={`/referensi/pemeriksaan-lab/${items.id}/detail`}
              tema="secondary"
              icon="eye"
              tooltip="Lihat Data"
              isIcon={true}
            />
          );
        default:
          return items[column_key];
      }
    },
    []
  );
  const props: PropsTable<any> = {
    columns,
    renderCell,
    data: result,
    pageInfo,
  };
  return (
    <div className="bg-default-50 p-6 rounded-md flex flex-col gap-6">
      <div className="flex justify-between items-center">
        <h1 className="font-medium text-xl">
          Daftar Pemeriksaan Laboratorium Patologi Klinik
        </h1>
        <ButtonNavigation
          href={`/referensi/pemeriksaan-lab/${detailId}/tambah-layanan`}
          tema="light"
          icon="plus"
          tooltip="Tambah Data"
          title="Layanan"
        />
      </div>
      <TablePoliklinik {...props} />
    </div>
  );
}
