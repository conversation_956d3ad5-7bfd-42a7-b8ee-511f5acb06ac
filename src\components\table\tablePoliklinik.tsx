"use client";

import UTable from "@/components/custom/table";
import { PropsTable } from "@/interfaces/tables";
import {
  Spinner,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  getKeyValue,
} from "@heroui/react";
import { useRouter } from "next/navigation";
import { useState, useMemo } from "react";

export default function TablePoli<T>({
  data = [],
  pageInfo,
  columns,
  renderCell,
  loading = false,
  basePath = "",
  parameter = false,
  isTab = false,
  isRender = false,
  rowKey = (item: any) => item?.id ?? Math.random().toString(),
  forceRenderKey,
}: PropsTable<T>) {
  const [page, setPage] = useState(0);
  const [sizePage, setSizePage] = useState(10);
  const router = useRouter();

  // Transform data menjadi rows dengan custom renderCell
  const rows = useMemo(() => {
    return data.map((item, index) => {
      const baseRow: any = {
        key: rowKey(item),
        originalItem: item,
        index: index
      };

      // Jika menggunakan custom renderCell, buat property untuk setiap column
      if (isRender && renderCell) {
        columns.forEach(column => {
          baseRow[column.key] = renderCell(item, column.key, index);
        });
      } else {
        // Jika tidak menggunakan custom renderCell, copy semua properties dari item
        Object.keys(item as any).forEach(key => {
          baseRow[key] = (item as any)[key];
        });
      }

      return baseRow;
    });
  }, [data, columns, renderCell, isRender, rowKey, forceRenderKey]);

  const pagination = {
    page: page,
    pageSize: sizePage,
    totalPage: pageInfo?.total_pages ?? 1,
    totalData: pageInfo?.total_elements ?? data.length,

    onChangePage: (page: number, pageSize: number) => {
      setPage(page);
      setSizePage(pageSize);
      if (basePath) {
        router.push(`${basePath}?page=${page}&size=${pageSize}`);
      }
    },
  };

  return (
    <div
      className={`bg-default-50 ${isTab ? "p-0" : "py-4 lg:py-6"} rounded-md`}
    >
      <UTable pagination={pagination} bordered={true} parameter={parameter}>
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              className="text-default-900"
              align={column.key === "action" ? "center" : "start"}
              key={column.key}
            >
              {column.label}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody
          emptyContent="Tidak ada data"
          loadingContent={<Spinner />}
          isLoading={loading}
        >
          {rows.map((row) => {
            // Tambahkan forceRenderKey ke key jika ada
            const baseKey = row.key;
            const forceKey = forceRenderKey ? forceRenderKey(row.originalItem) : '';
            const finalKey = forceKey ? `${baseKey}-${forceKey}` : baseKey;

            return (
              <TableRow key={finalKey}>
                {(columnKey) => (
                  <TableCell>
                    {getKeyValue(row, columnKey)}
                  </TableCell>
                )}
              </TableRow>
            );
          })}
        </TableBody>
      </UTable>
    </div>
  );
}
