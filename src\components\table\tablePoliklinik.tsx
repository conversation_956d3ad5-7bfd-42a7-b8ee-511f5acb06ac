"use client";

import UTable from "@/components/custom/table";
import { PropsTable } from "@/interfaces/tables";
import {
  Spin<PERSON>,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from "@heroui/react";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function TablePoli<T>({
  data = [],
  pageInfo,
  columns,
  renderCell,
  loading = false,
  basePath = "",
  parameter = false,
  isTab = false,
  isRender = false,
  rowKey = (item: any) => item?.id ?? Math.random().toString(),
}: PropsTable<T>) {
  const [page, setPage] = useState(0);
  const [sizePage, setSizePage] = useState(10);
  const router = useRouter();

  const pagination = {
    page: page,
    pageSize: sizePage,
    totalPage: pageInfo?.total_pages ?? 1,
    totalData: pageInfo?.total_elements ?? data.length,

    onChangePage: (page: number, pageSize: number) => {
      setPage(page);
      setSizePage(pageSize);
      if (basePath) {
        router.push(`${basePath}?page=${page}&size=${pageSize}`);
      }
    },
  };

  return (
    <div
      className={`bg-default-50 ${isTab ? "p-0" : "py-4 lg:py-6"} rounded-md`}
    >
      <UTable pagination={pagination} bordered={true} parameter={parameter}>
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              className="text-default-900"
              align={column.key === "action" ? "center" : "start"}
              key={column.key}
            >
              {column.label}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody
          items={data}
          emptyContent="Tidak ada data"
          loadingContent={<Spinner />}
          isLoading={loading}
        >
          {/* {(item) => (
            <TableRow key={rowKey(item)}>
              {(columnKey) => (
                <TableCell>
                  {renderCell(item, columnKey as string, data.indexOf(item))}
                </TableCell>
              )}
            </TableRow>
          )} */}
          {renderCell.map((row) =>
            <TableRow key={row.key}>
              {(columnKey) => <TableCell>{getKeyValue(row, columnKey)}</TableCell>}
            </TableRow>
          )}
        </TableBody>
      </UTable>
    </div>
  );
}
