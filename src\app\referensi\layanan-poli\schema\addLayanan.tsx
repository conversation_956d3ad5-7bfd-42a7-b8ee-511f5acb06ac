import * as yup from "yup";

export const layananAddSchema = yup.object({
  laboratorium: yup.string().required("Nama layanan wajib diisi"),
  pasienPengguna: yup
    .array()
    .min(1, "Pilih minimal satu jenis pasien")
    .required("Jenis pasien wajib dipilih"),
  STUDENT: yup.number().when("pasienPengguna", {
    is: (values: string[]) => values?.includes("STUDENT"),
    then: (schema) =>
      schema
        .required("Tarif untuk mahasiswa wajib diisi")
        .min(1, "Tarif harus lebih dari 0"),
    otherwise: (schema) => schema.notRequired().nullable(),
  }),
  EMPLOYEE: yup.number().when("pasienPengguna", {
    is: (values: string[]) => values?.includes("EMPLOYEE"),
    then: (schema) =>
      schema
        .required("Tarif untuk pegawai wajib diisi")
        .min(1, "Tarif harus lebih dari 0"),
    otherwise: (schema) => schema.notRequired(),
  }),
  FAMILY_OF_EMPLOYEE: yup.number().when("pasienPengguna", {
    is: (values: string[]) => values?.includes("FAMILY_OF_EMPLOYEE"),
    then: (schema) =>
      schema
        .required("Tarif untuk pegawai wajib diisi")
        .min(1, "Tarif harus lebih dari 0"),
    otherwise: (schema) => schema.notRequired(),
  }),
  PUBLIC: yup.number().when("pasienPengguna", {
    is: (values: string[]) => values?.includes("PUBLIC"),
    then: (schema) =>
      schema
        .required("Tarif untuk pegawai wajib diisi")
        .min(1, "Tarif harus lebih dari 0"),
    otherwise: (schema) => schema.notRequired(),
  }),
  ALUMNI: yup.number().when("pasienPengguna", {
    is: (values: string[]) => values?.includes("ALUMNI"),
    then: (schema) =>
      schema
        .required("Tarif untuk pegawai wajib diisi")
        .min(1, "Tarif harus lebih dari 0"),
    otherwise: (schema) => schema.notRequired(),
  }),
  RETIRED: yup.number().when("pasienPengguna", {
    is: (values: string[]) => values?.includes("RETIRED"),
    then: (schema) =>
      schema
        .required("Tarif untuk pegawai wajib diisi")
        .min(1, "Tarif harus lebih dari 0"),
    otherwise: (schema) => schema.notRequired(),
  }),
});

export type LayananAddSchema = yup.InferType<typeof layananAddSchema>;
