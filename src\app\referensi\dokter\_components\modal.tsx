"use client";

import { FormInput } from "@/components/custom/input";
import { FormSelect } from "@/components/custom/select";
import {
  addToast,
  AutocompleteItem,
  Button,
  Modal,
  ModalBody,
  ModalContent,
  <PERSON>dalFooter,
  <PERSON>dalHeader,
  Tooltip,
  useDisclosure,
} from "@heroui/react";
import { useFormik } from "formik";
import { Pencil } from "lucide-react";
import { useEffect, useState } from "react";
import { DokterUpdate } from "../schema/updatePoli";
import { getPolilist } from "../../poli/actions";

type payload = {
  tenaga_medis: string;
  penugasan: Array<string>;
};

const majors = [
  { key: "1", value: "arsat" },
  { key: "2", value: "teguh" },
  { key: "3", value: "maulana" },
];

export default function Modals({ id, data }: { id: string; data: any }) {
  const [isSubmit, setIsSubmit] = useState(false);
  const { isOpen, onOpen, onOpenChange } = useDisclosure(); // state modals

  const [loading, setLoading] = useState(false);
  const [poliList, setPoliList] = useState<any[]>([]);

  const getPoli = async () => {
    setLoading(true);
    try {
      const res = (await getPolilist()) as any;
      setPoliList(res);
      return res;
    } catch (error: any) {
      addToast({
        title: "Error",
        color: "danger",
        description: error.message ?? "Terjadi kesalahan",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      getPoli();
    }
  }, [isOpen]);

  const initialValues = {
    tenaga_medis: data?.full_name_with_degree,
    penugasan: data?.poly_associations.map((item: any) => item.id),
  };

  const handleSubmit = (value: typeof initialValues) => {
    setIsSubmit(true);
    alert(JSON.stringify(value));
    setIsSubmit(false);
  };

  const formik = useFormik<payload>({
    initialValues,
    enableReinitialize: true,
    validationSchema: DokterUpdate,
    onSubmit: handleSubmit,
  });

  return (
    <>
      <Tooltip content="Edit Data">
        <Button
          isIconOnly
          startContent={<Pencil className="w-4" />}
          color="secondary"
          size="sm"
          onPress={onOpenChange}
        />
      </Tooltip>
      <Modal isOpen={isOpen} onOpenChange={onOpenChange} backdrop={"blur"}>
        <ModalContent>
          {(onClose) => (
            <>
              <form onSubmit={formik.handleSubmit}>
                <ModalHeader className="flex flex-col gap-1">
                  Update Tugas Dokter
                </ModalHeader>
                <ModalBody className="mb-4">
                  <div className="flex flex-col gap-4">
                    <div className="flex flex-col gap-1">
                      <label htmlFor="poli" className="text-sm ">
                        {" "}
                        Nama Tenaga Medis
                      </label>
                      <FormInput
                        name={"tenaga_medis"}
                        isNumeric={false}
                        formik={formik}
                        placeholder="Ketik nama tenaga medis"
                      />
                    </div>
                    <div className="flex flex-col gap-1 w-full">
                      <label htmlFor="major_code" className="text-sm ">
                        Penugasan
                      </label>
                      <FormSelect
                        defaultSelectedKeys={data?.poly_associations.map(
                          (item: any) => item.name
                        )}
                        selectionMode="multiple"
                        name="penugasan"
                        classNames={{ base: "bg-white dark:bg-inherit" }}
                        formik={formik}
                        placeholder={`Cari Penugasan`}
                        radius="sm"
                        variant="bordered"
                        isLoading={loading}
                      >
                        {poliList?.map((unit) => (
                          <AutocompleteItem key={unit.key}>
                            {unit?.name}
                          </AutocompleteItem>
                        ))}
                      </FormSelect>
                    </div>
                  </div>
                </ModalBody>
                <ModalFooter>
                  <Button
                    className="bg-default-50 border-[1px] border-md"
                    onPress={() => {
                      onClose();
                      formik.resetForm();
                    }}
                  >
                    Kembali
                  </Button>
                  <Button color="primary" type="submit" isLoading={isSubmit}>
                    Simpan
                  </Button>
                </ModalFooter>
              </form>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
