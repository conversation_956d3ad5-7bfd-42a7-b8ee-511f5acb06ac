export type FormValues = {
    laboratorium: string;
    pasienPengguna: string[];
    STUDENT?: string;
    EMPLOYEE?: string;
    FAMILY_OF_EMPLOYEE?: string;
    PUBLIC?: string;
    ALUMNI?: string;
    RETIRED?: string;
};

export type intialLayanan = {
    laboratorium: string
    pasienPengguna: string[];
}

export type ApiUpdate = {
    poly_id: string;
    name: string;
    tariffs: {
        id: string;
        category: string;
        price: number;
    }[];
    active: boolean;
};

export type subParameter = {
    id?: string;
    name: string;
    type: string;
    min_value?: string | null;
    max_value?: string | null;
    value?: string | null
    examination_unit: string;
};

export type parameter = {
    id?: string;
    title: string;
    subParameter: subParameter[] | null;
};

export type parameterAPI = {
    laboratory_id: string,
    service_id: string,
    name: string,
    medical_examination: [
        {
            name:string,
            type: "RANGE" | "GTE" | "EQ" | "LTE",
            min_value: number,
            max_value: number,
            examination_unit: string
        },
    ]
}