import * as yup from "yup";
export const addSubParameterYup = yup.object({
  name: yup.string().required("Nama parameter wajib diisi"),
  type: yup.string().required("Tipe parameter wajib diisi"),
  examination_unit: yup.string().required("Satuan pemeriksaan wajib diisi"),

  // Conditional validation berdasarkan tipe
  min_value: yup.string().when('type', {
    is: (type: string) => type === 'RANGE',
    then: (schema) => schema.required("Nilai minimum wajib diisi untuk tipe range"),
    otherwise: (schema) => schema.nullable().optional()
  }),

  max_value: yup.string().when('type', {
    is: (type: string) => type === 'RANGE',
    then: (schema) => schema.required("Nilai maksimum wajib diisi untuk tipe range"),
    otherwise: (schema) => schema.nullable().optional()
  }),

  value: yup.string().when('type', {
    is: (type: string) => ['GTE', 'EQ', 'LTE'].includes(type),
    then: (schema) => schema.required("Nilai rujukan wajib diisi"),
    otherwise: (schema) => schema.nullable().optional()
  })
});

export type addSubParameterYup = yup.InferType<typeof addSubParameterYup>;

export const addTitleYup = yup.object({
  name: yup.string().required("Nama layanan wajib diisi"),
});

export type addTitleYup = yup.InferType<typeof addTitleYup>;
