import { ApiUpdate, intial<PERSON>ayanan, parameter, parameterAPI } from "@/interfaces/refreence/service";

export function maping<PERSON><PERSON>nan(dataUpdate: ApiUpdate | null):intial<PERSON>ayanan {
     return  {
    poly_id: dataUpdate?.poly_id,
    laboratorium: dataUpdate?.name,
    pasienPengguna: dataUpdate?.tariffs?.map((e: any) => e.category),
    ...dataUpdate?.tariffs?.reduce((acc: any, e: any) => {
      acc[e.category] = e.price;
      return acc;
    }, {} as Record<string, number>),
  };
}

export function mapingParameter(dataUpdate: parameterAPI[] | null): parameter[] {
    if (!dataUpdate || !Array.isArray(dataUpdate)) return [];

    return dataUpdate.map((parameterGroup, groupIndex) => ({
        id: (groupIndex + 1).toString(),
        title: parameterGroup.name,
        subParameter: parameterGroup.medical_examination.map((e, index) => ({
            id: `${groupIndex + 1}-${index + 1}`,
            name: e.name,
            type: e.type,
            min_value: e.type === 'RANGE' ? e.min_value?.toString() : null,
            max_value: e.type === 'RANGE' ? e.max_value?.toString() : null,
            value: e.type !== 'RANGE' ? e.min_value?.toString() : null,
            examination_unit: e.examination_unit,
        })),
    }));
}
