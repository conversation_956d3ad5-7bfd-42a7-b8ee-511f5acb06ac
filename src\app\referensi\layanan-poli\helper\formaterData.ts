import { ApiUpdate, intial<PERSON>ayanan, parameter, parameterAPI } from "@/interfaces/refreence/service";

export function mapingLayanan(dataUpdate: ApiUpdate | null):intialLayanan {
     return  {
    poly_id: dataUpdate?.poly_id,
    laboratorium: dataUpdate?.name,
    pasienPengguna: dataUpdate?.tariffs?.map((e: any) => e.category),
    ...dataUpdate?.tariffs?.reduce((acc: any, e: any) => {
      acc[e.category] = e.price;
      return acc;
    }, {} as Record<string, number>),
  };
}

export function mapingParameter(dataUpdate: parameterAPI[] | null): parameter[] {}
