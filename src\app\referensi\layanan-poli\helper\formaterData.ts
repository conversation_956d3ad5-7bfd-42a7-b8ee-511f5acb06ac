import { ApiUpdate, intialLayanan, parameter, parameterAPI, subParameter } from "@/interfaces/refreence/service";

export function mapingLayanan(dataUpdate: ApiUpdate | null):intialLayanan {
     return  {
    poly_id: dataUpdate?.poly_id,
    laboratorium: dataUpdate?.name,
    pasienPengguna: dataUpdate?.tariffs?.map((e: any) => e.category),
    ...dataUpdate?.tariffs?.reduce((acc: any, e: any) => {
      acc[e.category] = e.price;
      return acc;
    }, {} as Record<string, number>),
  };
}

export function mapingParameter(dataUpdate: parameterAPI | null): parameter[] {
    return dataUpdate?.medical_examination.map((e: subParameter) => ({
        id: e.id,
        title: e.name,
        subParameter: e.subParameter,
      }));
}
