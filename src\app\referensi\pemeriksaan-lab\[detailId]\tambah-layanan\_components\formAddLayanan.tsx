"use client";

import { FormInput } from "@/components/custom/input";
import { PATIENT_TYPE_LIST } from "@/constant/constant";
import { addToast, Button, Checkbox, CheckboxGroup } from "@heroui/react";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";

import { layananAddSchema } from "@/app/referensi/layanan-poli/schema/addLayanan";
import { Plus, Trash } from "lucide-react";
import { useState } from "react";
import ModalSubList from "./modalPost";
import ModalTitle from "./modalTitle";
import TableParameter from "./tableParameter";

type FormValues = {
  laboratorium: string;
  pasienPengguna: string[];
  STUDENT?: string;
  EMPLOYEE?: string;
  FAMILY_OF_EMPLOYEE?: string;
  PUBLIC?: string;
  ALUMNI?: string;
  RETIRED?: string;
};

const initialValues = {
  laboratorium: "",
  pasienPengguna: [] as string[],
};

export type subParameter = {
  id?: string;
  name: string;
  type: string;
  min_value?: string | null;
  max_value?: string | null;
  value?:string | null
  examination_unit: string;
};

type parameter = {
  id?: string;
  title: string;
  subParameter: subParameter[];
};

export default function FormAddLayanan({ detailId }: { detailId: string }) {
  const id = Date.now().toString();
  const initParameter = {
    id: id,
    title: "",
    subParameter: [],
  };

  const [parameters, setParameters] = useState<Array<parameter>>([
    initParameter,
  ]);

  const addParameters = () => setParameters((e) => [...e, initParameter]);
  const removeParameters = (id: string) => {
    if (parameters.length === 1) return;
    setParameters(parameters.filter((e) => e.id !== id));
  };

  const addSubParameter = (
    parameterId: string,
    initialValues: Partial<subParameter> = {}
  ) => {
    setParameters(
      parameters.map((param) =>
        param.id === parameterId
          ? {
              ...param,
              subParameter: [
                ...param.subParameter,
                {
                  id: id,
                  name: initialValues.name || "",
                  min_value: initialValues.min_value || "",
                  max_value: initialValues.min_value || "",
                  value: initialValues.value || "",
                  examination_unit: initialValues.examination_unit || "",
                },
              ],
            }
          : param
      )
    );
  };

  const updateTitle = (parameterId: string, title: string) => {
    setParameters(
      parameters.map((param) =>
        param.id === parameterId ? { ...param, title } : param
      )
    );
  };

  const deleteSubParameter = ({
    parameterId,
    subParamId,
  }: {
    parameterId: string;
    subParamId: string;
  }) => {
    setParameters((prevParameters) =>
      prevParameters.map((param) => {
        // Skip jika bukan parameter yang dituju
        if (param.id !== parameterId) return param;

        // Pastikan subParameter ada sebelum memfilter
        if (!param.subParameter || param.subParameter.length === 0)
          return param;

        // Filter subParameter (hanya hapus yang ID-nya match)
        const filteredSubParams = param.subParameter.filter(
          (subParam) => subParam.id !== subParamId
        );

        // Return parameter dengan subParameter yang sudah difilter
        return {
          ...param,
          subParameter: filteredSubParams,
        };
      })
    );
  };

  // const updateSubParameter = (
  //   parameterId: string,
  //   subParamId: string,
  //   updatedData: Partial<subParameter>
  // ) => {
  //   setParameters(
  //     parameters.map((param) =>
  //       param.id === parameterId
  //         ? {
  //             ...param,
  //             subParameter: param.subParameter.map((subParam) =>
  //               subParam.id === subParamId
  //                 ? { ...subParam, ...updatedData }
  //                 : subParam
  //             ),
  //           }
  //         : param
  //     )
  //   );
  // };

  const router = useRouter();

  const handleSubmit = async (values: FormValues) => {
    const createTariff = (category: string, includeId: boolean = false) => {
      const price = parseInt(
        (values[category as keyof FormValues] as string) ?? "0"
      );
      const tariff: any = { category, price };

      if (includeId) {
        tariff.id = "1";
      }

      return tariff;
    };

    const format: any = {
      laboratory_id: detailId,
      name: values.laboratorium,
      tariffs: PATIENT_TYPE_LIST.map((item) =>
        createTariff(item.value, false)
      ).filter((item) => item.price > 0),
    };
    console.log({parameters, format});
    
    // formik.setSubmitting(true);
    // try {
    //  const res = await postLayananPoli({ data: format, poly_id: id ?? "" });
    //   if (res.error) {
    //     throw new Error(res?.message);
    //   }
    //   addToast({
    //     title: "Berhasil menambahkan data",
    //     color: "success",
    //   });
    //   window.location.reload();
    // } catch (error: any) {
    //   addToast({
    //     title: "Error",
    //     color: "danger",
    //     description: error.message ?? "Terjadi kesalahan",
    //   });
    // } finally {
    //   formik.setSubmitting(false);
    // }
  };

  const formik = useFormik({
    initialValues,
    validationSchema: layananAddSchema,
    onSubmit: (values) => handleSubmit(values),
  });

  // Handler untuk checkbox
  const handleCheckboxChange = (selectedValues: string[]) => {
    formik.setFieldValue("pasienPengguna", selectedValues);
  };

  const getTarifFieldName = (key: string): { label: string; value: string } => {
    switch (key) {
      case PATIENT_TYPE_LIST[0].value:
        return {
          label: PATIENT_TYPE_LIST[0].label,
          value: PATIENT_TYPE_LIST[0].value,
        };
      case PATIENT_TYPE_LIST[1].value:
        return {
          label: PATIENT_TYPE_LIST[1].label,
          value: PATIENT_TYPE_LIST[1].value,
        };
      case PATIENT_TYPE_LIST[2].value:
        return {
          label: PATIENT_TYPE_LIST[2].label,
          value: PATIENT_TYPE_LIST[2].value,
        };
      case PATIENT_TYPE_LIST[3].value:
        return {
          label: PATIENT_TYPE_LIST[3].label,
          value: PATIENT_TYPE_LIST[3].value,
        };
      case PATIENT_TYPE_LIST[4].value:
        return {
          label: PATIENT_TYPE_LIST[4].label,
          value: PATIENT_TYPE_LIST[4].value,
        };
      case PATIENT_TYPE_LIST[5].value:
        return {
          label: PATIENT_TYPE_LIST[5].label,
          value: PATIENT_TYPE_LIST[5].value,
        };
      default:
        return {
          label: "",
          value: "",
        };
    }
  };

  return (
    <form onSubmit={formik.handleSubmit}>
      <div className="rounded-md w-full flex flex-col gap-4 justify-center items-center">
        <div className="flex-col gap-6 flex bg-default-50 lg:w-1/2 w-full p-6 rounded-md">
          <h1 className="text-xl font-medium">Form Tambah Layanan</h1>

          {/* Field Nama Layanan */}
          <div className="flex flex-col gap-1">
            <label htmlFor="laboratorium" className="text-sm">
              Nama Layanan
            </label>
            <FormInput
              name="laboratorium"
              isNumeric={false}
              formik={formik}
              placeholder="Ketik nama laboratorium"
            />
          </div>

          {/* Pasien Pengguna Layanan */}
          <div className="flex flex-col gap-2">
            <label className="text-sm">Pasien Pengguna Layanan</label>
            <CheckboxGroup
              size="sm"
              value={formik.values.pasienPengguna}
              onChange={handleCheckboxChange}
              name="pasienPengguna"
            >
              {PATIENT_TYPE_LIST.map((item) => (
                <Checkbox key={item.value} value={item.value}>
                  {item.label}
                </Checkbox>
              ))}
            </CheckboxGroup>
            {formik.touched.pasienPengguna && formik.errors.pasienPengguna && (
              <div className="text-red-500 text-sm">
                {formik.errors.pasienPengguna}
              </div>
            )}
          </div>

          <hr />

          {/* Section Tarif */}
          <div className="flex flex-col gap-2">
            <h1 className="font-medium">Tarif</h1>
            {formik.values.pasienPengguna.length === 0 && (
              <p className="text-sm text-default-500">
                Pilih pasien pengguna layanan terlebih dahulu untuk menentukan
                tarif!
              </p>
            )}
          </div>

          {/* Conditional Tarif Fields */}
          <div className="flex flex-col gap-6">
            {formik.values.pasienPengguna.length > 0 &&
              formik.values.pasienPengguna.map((key, index) => (
                <div className="flex flex-col gap-1" key={key}>
                  <label
                    htmlFor={getTarifFieldName(key).value as string}
                    className="text-sm capitalize"
                  >
                    {getTarifFieldName(key).label}
                  </label>
                  <FormInput
                    name={getTarifFieldName(key).value as any}
                    isNumeric={true}
                    formik={formik}
                    placeholder="Rp.0"
                  />
                </div>
              ))}
          </div>
        </div>

        {parameters.map((e, index) => (
          <div
            key={e.id}
            className="flex-col gap-6 flex bg-default-50 lg:w-1/2 w-full p-6 rounded-md"
          >
            <div className="flex flex-col gap-2">
              <div className="flex items-center justify-between">
                <div className="flex gap-1 items-center justify-between">
                  <h1 className="text-lg font-medium">
                    {e.title || "Parameter"}
                  </h1>
                  <ModalTitle updateTitle={updateTitle} id={e.id as string} />
                </div>
                {parameters.length > 1 && (
                  <Button
                    isIconOnly
                    color="danger"
                    size="sm"
                    onPress={() => removeParameters(e.id as string)}
                  >
                    <Trash className="w-4" />
                  </Button>
                )}
              </div>
              <div className="">
                <ModalSubList
                  idParameters={e.id as string}
                  addSubParameter={addSubParameter}
                />
              </div>
            </div>
            <div className="">
              <TableParameter
                data={e.subParameter}
                idParameter={e.id as string}
                deleteSubParameter={deleteSubParameter}
              />
            </div>
          </div>
        ))}
      </div>
      <div className="rounded-md w-full flex flex-col gap-4 items-center">
        <div className="flex lg:w-1/2 w-full">
          <div className="flex  gap-4  justify-start mt-4 w-full lg:w-1/2 ">
            <Button
              startContent={<Plus className="w-4" />}
              color="primary"
              onPress={addParameters}
            >
              Tambah Parameter
            </Button>
          </div>
          <div className="flex  gap-4 justify-end mt-4 w-full lg:w-1/2 ">
            <Button
              className={`bg-default-50 border-[1px] border-md`}
              type="button"
              onPress={() => {
                formik.resetForm();
                router.back();
              }}
            >
              Kembali
            </Button>
            <Button
              // className={`${!formik.isValid && "bg-default-500"}`}
              color="primary"
              type="submit"
              isLoading={formik.isSubmitting}
              // disabled={formik.isSubmitting || !formik.isValid}
            >
              Simpan
            </Button>
          </div>
        </div>
      </div>
    </form>
  );
}
