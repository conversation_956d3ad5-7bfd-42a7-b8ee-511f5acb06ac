"use client";

import { FormInput } from "@/components/custom/input";
import { addToast, Button, Checkbox, CheckboxGroup } from "@heroui/react";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";
import { PATIENT_TYPE_LIST } from "@/constant/constant";
import { layananAddSchema } from "@/app/referensi/layanan-poli/schema/addLayanan";
import {
  patchLayananPoli,
  postLayananPoli,
} from "@/app/referensi/layanan-poli/actions";
import { use } from "react";
import { ApiUpdate, FormValues } from "@/interfaces/refreence/service";
import { mapingLayanan } from "@/app/referensi/layanan-poli/helper/formaterData";


export default function CompFormAddLayanan({
  mode = "add",
  data,
  id,
}: {
  mode: "add" | "edit";
  data?: any;
  id?: string;
}) {
  const dataUpdate: ApiUpdate | null =
    mode === "edit" && data ? use(data) : null;
  const initialValues = {
    laboratorium: "",
    pasienPengguna: [] as string[],
  };

  const router = useRouter();

  const handleSubmit = async (values: FormValues) => {
    const createTariff = (category: string, includeId: boolean = false) => {
      const price = parseInt(
        (values[category as keyof FormValues] as string) ?? "0"
      );
      const tariff: any = { category, price };

      if (includeId && mode === "edit") {
        tariff.id = dataUpdate?.tariffs?.find(
          (e: any) => e.category === category
        )?.id;
      }

      return tariff;
    };

    const tariffs = PATIENT_TYPE_LIST.map((item) =>
      createTariff(item.value, mode === "edit")
    ).filter((item) => item.price > 0);

    const format: any = {
      name: values.laboratorium,
      tariffs,
    };

    formik.setSubmitting(true);
    try {
      let res;
      if (mode === "add") {
        res = await postLayananPoli({ data: format, poly_id: id ?? "" });
      } else {
        res = await patchLayananPoli({
          data: format,
          id: id ?? "",
          active: dataUpdate?.active ?? false,
        });
      }
      if (res.error) {
        throw new Error(res?.message);
      }
      addToast({
        title: "Berhasil menambahkan data",
        color: "success",
      });
      window.location.reload();
    } catch (error: any) {
      addToast({
        title: "Error",
        color: "danger",
        description: error.message ?? "Terjadi kesalahan",
      });
    } finally {
      formik.setSubmitting(false);
    }
  };
  
  const formik = useFormik({
    initialValues: mode === "add" ? initialValues : mapingLayanan(dataUpdate),
    validationSchema: layananAddSchema,
    onSubmit: handleSubmit,
  });

  const handleCheckboxChange = (selectedValues: string[]) => {
    const newValues: any = {
      laboratorium: formik.values.laboratorium,
      pasienPengguna: selectedValues,
    };

    selectedValues.forEach((value) => {
      const fieldInfo = getTarifFieldName(value);
      if (fieldInfo && fieldInfo.value) {
        newValues[fieldInfo.value] =
          formik.values[fieldInfo.value as keyof typeof formik.values] || "";
      }
    });

    formik.setValues(newValues);
  };

  const getTarifFieldName = (key: string): { label: string; value: string } => {
    switch (key) {
      case PATIENT_TYPE_LIST[0].value:
        return {
          label: PATIENT_TYPE_LIST[0].label,
          value: PATIENT_TYPE_LIST[0].value,
        };
      case PATIENT_TYPE_LIST[1].value:
        return {
          label: PATIENT_TYPE_LIST[1].label,
          value: PATIENT_TYPE_LIST[1].value,
        };
      case PATIENT_TYPE_LIST[2].value:
        return {
          label: PATIENT_TYPE_LIST[2].label,
          value: PATIENT_TYPE_LIST[2].value,
        };
      case PATIENT_TYPE_LIST[3].value:
        return {
          label: PATIENT_TYPE_LIST[3].label,
          value: PATIENT_TYPE_LIST[3].value,
        };
      case PATIENT_TYPE_LIST[4].value:
        return {
          label: PATIENT_TYPE_LIST[4].label,
          value: PATIENT_TYPE_LIST[4].value,
        };
      case PATIENT_TYPE_LIST[5].value:
        return {
          label: PATIENT_TYPE_LIST[5].label,
          value: PATIENT_TYPE_LIST[5].value,
        };
      default:
        return {
          label: "",
          value: "",
        };
    }
  };

  return (
    <form onSubmit={formik.handleSubmit}>
      <div className="rounded-md w-full flex flex-col justify-center items-center pb-4">
        <div className="flex-col gap-6 flex bg-default-50 lg:w-1/2 w-full p-6 rounded-md">
          <h1 className="text-xl font-medium">
            Form {mode === "add" ? "Tambah" : "Update"} Layanan
          </h1>

          {/* Field Nama Layanan */}
          <div className="flex flex-col gap-1">
            <label htmlFor="laboratorium" className="text-sm">
              Nama Layanan
            </label>
            <FormInput
              name="laboratorium"
              isNumeric={false}
              formik={formik}
              placeholder="Ketik nama laboratorium"
            />
          </div>

          <div className="flex flex-col gap-2">
            <label className="text-sm">Pasien Pengguna Layanan</label>
            <CheckboxGroup
              isDisabled={mode === "edit"}
              size="sm"
              value={formik.values.pasienPengguna}
              onChange={handleCheckboxChange}
              name="pasienPengguna"
            >
              {PATIENT_TYPE_LIST.map((item) => (
                <Checkbox key={item.value} value={item.value}>
                  {item.label}
                </Checkbox>
              ))}
            </CheckboxGroup>
            {formik.touched.pasienPengguna && formik.errors.pasienPengguna && (
              <div className="text-red-500 text-sm">
                {formik.errors.pasienPengguna}
              </div>
            )}
          </div>

          <hr />

          {/* Section Tarif */}
          <div className="flex flex-col gap-2">
            <h1 className="font-medium">Tarif</h1>
            {formik.values.pasienPengguna?.length === 0 && (
              <p className="text-sm text-default-500">
                Pilih pasien pengguna layanan terlebih dahulu untuk menentukan
                tarif!
              </p>
            )}
          </div>

          {/* Conditional Tarif Fields */}
          <div className="flex flex-col gap-6">
            {formik.values.pasienPengguna?.length > 0 &&
              formik.values.pasienPengguna?.map((key, index) => (
                <div className="flex flex-col gap-1" key={key}>
                  <label
                    htmlFor={getTarifFieldName(key).value as string}
                    className="text-sm capitalize"
                  >
                    {getTarifFieldName(key).label}
                  </label>
                  <FormInput
                    startContent={(<div className="text-sm border-r-[1px] border-default-400 pr-[6px]">Rp</div>)}
                    name={getTarifFieldName(key).value as any}
                    isNumeric={true}
                    formik={formik}
                    placeholder="Rp.0"
                  />
                </div>
              ))}

            <div className="flex items-center gap-4 justify-end mt-4">
              <Button
                className={`bg-default-50 border-[1px] border-md `}
                type="button"
                onPress={() => {
                  formik.resetForm();
                  router.back();
                }}
              >
                Kembali
              </Button>
              <Button
                className={`${!formik.isValid && "bg-default-500"}`}
                color="primary"
                type="submit"
                isLoading={formik.isSubmitting}
                disabled={formik.isSubmitting || !formik.isValid}
              >
                Simpan
              </Button>
            </div>
          </div>
        </div>
      </div>
    </form>
  );
}
