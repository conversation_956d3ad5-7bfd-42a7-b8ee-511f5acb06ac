import { Suspense } from "react";
import { getLabyId } from "../actions";
import TableDetailPemeriksaanLab from "./_components/table";
import LoadingTable from "@/components/loadingTable";

export default async function PageDetailLayananLab({ params }: any) {
  const { detailId: id } = await params;
  const res = getLabyId({ id });
  return (
    <div className="flex flex-col gap-6 lg:pt-4 pt-0">
      <h1 className="font-medium text-xl">Laboratorium Patologi Klinik</h1>
      <Suspense fallback={<LoadingTable />}>
        <TableDetailPemeriksaanLab detailId={id} res={res} />
      </Suspense>
    </div>
  );
}
