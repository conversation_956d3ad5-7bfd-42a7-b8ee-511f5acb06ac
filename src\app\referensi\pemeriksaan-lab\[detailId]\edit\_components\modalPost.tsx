"use client";

import { FormInput } from "@/components/custom/input";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalHeader,
  Radio,
  RadioGroup,
  useDisclosure,
} from "@heroui/react";
import { useFormik } from "formik";
import { Plus } from "lucide-react";

import { subParameter } from "./formAddLayanan";
import { addSubParameterYup } from "../schema/addLayanan";

type payload = {
  id?: string;
  name: string;
  type: string;
  min_value?: string | null;
  max_value?: string | null;
  value?:string | null
  examination_unit: string;
};

export default function ModalSubList({
  idParameters,
  addSubParameter,
}: {
  idParameters: string;
  addSubParameter: (
    parameterId: string,
    updatedData: Partial<subParameter>
  ) => void;
}) {
  const { isOpen, onOpen, onOpenChange } = useDisclosure(); // state modals

  const initialValues = {
    name: "",
    type: "",
    min_value: "",
    max_value: "",
    value: "",
    examination_unit: "",
  };

  const handleSubmit = async (value: subParameter) => {
    addSubParameter(idParameters, value);
    formik.resetForm();
    onOpenChange();
  };

  const formik = useFormik<payload>({
    initialValues,
    enableReinitialize: true,
    validationSchema: addSubParameterYup,
    onSubmit: handleSubmit,
  });

  const choiceRujukan = () => {
    switch (formik.values.type) {
      case "RANGE":
        return (
          <div className="flex items-center gap-4 p-4 rounded-md bg-semantic-info-50">
            <div className="flex flex-col gap-1">
              <label htmlFor="nilai" className="text-[13px] ">
                {" "}
                Nilai Minumum
              </label>
              <FormInput
                isClearable={false}
                name={"min_value"}
                isNumeric={false}
                formik={formik}
                placeholder="masukan nilai"
              />
            </div>
            <div className="flex flex-col gap-1">
              <label htmlFor="nilai" className="text-[13px] ">
                {" "}
                Nilai Maximum
              </label>
              <FormInput
                isClearable={false}
                name={"max_value"}
                isNumeric={false}
                formik={formik}
                placeholder="masukan nilai"
              />
            </div>
          </div>
        );
      case "GTE":
        return (
          <div className="flex flex-col gap-1">
            <label htmlFor="nilai" className="text-sm ">
              {" "}
              Nilai Rujukan
            </label>
            <FormInput
              isClearable={false}
              name={"value"}
              isNumeric={false}
              formik={formik}
              placeholder="masukan nilai"
            />
          </div>
        );
      case "EQ":
        return (
          <div className="flex flex-col gap-1">
            <label htmlFor="nilai" className="text-sm ">
              {" "}
              Nilai Rujukan
            </label>
            <FormInput
              isClearable={false}
              name={"value"}
              isNumeric={false}
              formik={formik}
              placeholder="masukan nilai"
            />
          </div>
        );
      case "LTE":
        return (
          <div className="flex flex-col gap-1">
            <label htmlFor="nilai" className="text-sm ">
              {" "}
              Nilai Rujukan
            </label>
            <FormInput
              isClearable={false}
              name={"value"}
              isNumeric={false}
              formik={formik}
              placeholder="masukan nilai"
            />
          </div>
        );
    }
  };

  return (
    <>
      <Button
        startContent={<Plus className="w-4" />}
        color="default"
        className="bg-default-50 border-2"
        size="sm"
        onPress={onOpenChange}
      >
        Tambah Jenis Pemeriksaan
      </Button>
      <Modal
        size="lg"
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        backdrop={"blur"}
      >
        <ModalContent>
          {(onClose) => (
            <>
              <form onSubmit={formik.handleSubmit}>
                <ModalHeader className="flex flex-col gap-1">
                  Tambah jenis pemeriksaan
                </ModalHeader>
                <ModalBody>
                  <div className="flex flex-col gap-6">
                    <div className="flex flex-col gap-1">
                      <label htmlFor="jenis" className="text-sm ">
                        {" "}
                        Jenis Pemeriksaan
                      </label>
                      <FormInput
                        isClearable={false}
                        name={"name"}
                        isNumeric={false}
                        formik={formik}
                        placeholder="masukan jenis"
                      />
                    </div>

                    <div className="flex flex-col gap-4">
                      <RadioGroup
                        label="Nilai Rujukan"
                        className="text-sm "
                        size="sm"
                        name="type"
                        onValueChange={(value) =>
                          formik.setFieldValue("type", value)
                        }
                      >
                        <Radio value="RANGE">Range</Radio>
                        <Radio value="GTE">&ge; (Lebih dari sama dengan)</Radio>
                        <Radio value="EQ">{`== (Sama dengan)`}</Radio>
                        <Radio value="LTE">&le; (Kurang dari sama dengan)</Radio>
                      </RadioGroup>
                    </div>

                    <div className="">{choiceRujukan()}</div>

                    <div className="flex flex-col gap-1">
                      <label htmlFor="satuan" className="text-sm ">
                        {" "}
                        Satuan
                      </label>
                      <FormInput
                        isClearable={false}
                        name={"examination_unit"}
                        isNumeric={false}
                        formik={formik}
                        placeholder="masukan Satuan"
                      />
                    </div>
                  </div>
                </ModalBody>
                <ModalFooter>
                  <Button
                    className="bg-default-50 border-[1px] border-md"
                    onPress={onClose}
                  >
                    Kembali
                  </Button>
                  <Button
                    color="primary"
                    type="submit"
                    isLoading={formik.isSubmitting}
                  >
                    Simpan
                  </Button>
                </ModalFooter>
              </form>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
